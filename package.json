{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@sendgrid/mail": "^8.1.4", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "bcryptjs": "^2.4.3", "cloudinary": "^2.5.1", "concurrently": "^9.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-fileupload": "^1.5.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.8.4", "paytmchecksum": "^1.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.3.0", "react-scripts": "^5.0.1", "request": "^2.88.2", "sqlite3": "^5.1.7", "validator": "^13.12.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "concurrently \"node ./src/backend/server.js\" \"react-scripts start\"", "build": "react-scripts build", "test": "react-scripts test", "prepare": "npm start", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@fortawesome/fontawesome-free": "^6.1.1"}}