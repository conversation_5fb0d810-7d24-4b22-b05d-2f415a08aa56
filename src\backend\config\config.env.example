# This is a example .env file for use in local development.
# Duplicate this file as .env in the root of the project and update the environment variables to match your desired config

# The HTTP port to run the application on
PORT=4000

# MongoDB connection string
MONGO_URI=mongodb+srv://username:<EMAIL>/Database

# JWT secret and expiry
JWT_SECRET=WFFWf15115U842UGUBWF81EE858UYBY51BGBJ5E51Q
JWT_EXPIRE=7d

# Cookie expiry
COOKIE_EXPIRE=5

# Stripe API Key & Secret Key (Optional) **Alternative Paytm**
STRIPE_API_KEY=test_api_key
STRIPE_SECRET_KEY=test_secret_key

# Any SMTP Credentials (Optional) **Alternative Sendgrid**
SMTP_HOST=smtp.gmail.com
SMTP_PORT=465
SMTP_SERVICE=gmail
SMTP_MAIL=<EMAIL>
SMTP_PASSWORD=password@123

# Sendgrid Credentials
SENDGRID_API_KEY=SG.apikey
SENDGRID_MAIL=<EMAIL>
SENDGRID_RESET_TEMPLATEID=d-sf5sf7s1g5ff8fd87df48dferg1
SENDGRID_ORDER_TEMPLATEID=d-sf5sf7s1g5ff8fd87df48df51eg

# Cloudinary Credentials
CLOUDINARY_NAME=store
CLOUDINARY_API_KEY=7467848571151
CLOUDINARY_API_SECRET=secret_key

# Paytm Credentials
PAYTM_MID=dgfg515451514451
PAYTM_MERCHANT_KEY=848dvdvdv848dv
PAYTM_WEBSITE=WEBSTAGING
PAYTM_CHANNEL_ID=WEB
PAYTM_INDUSTRY_TYPE=Retail
PAYTM_CUST_ID=dgfg515451514451

# The environment to run the application in
NODE_ENV=development