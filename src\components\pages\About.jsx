import React from 'react'
import "./About.css";
import Gift from '../misc/Gift';
import about_img from "../../images/about-image.png"
import { useEffect } from 'react';

const About = () => {

    useEffect(() => {
        window.scrollTo(0, 0)
    })
    return (
        <React.Fragment>
            <section className="about">
                <h1 className='page-heading'>About Us</h1>
                <div className="about-top">
                    <div className='about-top-1'> 
                        <h3> How does TFOR work?</h3>
                        <p>
                            Our team of experts selects and negotiates the acquisition of investment properties. These properties are made up of several lots, generally already rented. They allow us to carry out high value-added real estate transactions that are usually inaccessible to the majority of private individuals. <br/> <br/> For each property to be financed, we divide the purchase price by 10$ to define the supply of the NFT collection associated with the properties. <br/> <br/> When you collect, you can invest money in the property or properties of your choice by purchasing one or more NFTs.
                        </p>
                    </div>

                    <p className='about-top-2'>
                        <img src={about_img} className="about-image" alt="A building"/>
                    </p>
                </div>
                <br/>
                <div className='about-bottom'>
                    <div className='about-bottom-1'>
                        <h3>Build your assets with NFT</h3>
                        <p>
                            Each NFT entitles you to royalties based on the rental income generated by the building. <br/> <br/> Each month we pay you a fraction of the rent collected, in proportion to the number of NFTs you own. This payment is made each month to your wallet holding the NFT. <br/> <br/>At any time, you can buy or sell more NFTs through the secondary market.
                        </p>
                    </div>

                    <div className='about-bottom-2'>
                        <h3>Real estate investment really is accessible to everyone</h3>
                        <p>
                            Our investment opportunities are accessible to everyone: beginners or experienced investors, regardless of income, and regardless of your professional situation. <br/> <br/> With RoyalCity, you don't need to obtain a mortgage, and you can finally invest in real estate without any personal contribution. <br/> <br/> Whether you are a student, a young worker, a self-employed person, a liberal profession, a company director, an employee or a retiree: Welcome to RoyalCity!
                        </p>
                    </div>
                </div>
                <br/>
                <br/>
                <Gift/>
            </section>
        </React.Fragment>
    )
}

export default About;