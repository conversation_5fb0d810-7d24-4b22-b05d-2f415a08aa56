const legalQ = [
  {
    question: "What is Royalty financing",
    answer: "When you invest in an asset through an NFT RoyalCity, you are bound by a royalty agreement. The royalty agreement is evidence that we are committed to paying you a percentage of our turnover at a constant frequency over the term of the agreement. The royalty regime is a contractual regime in addition to the regulated statutes (CIP, IFP), and based on common consumer and contract law. The financing operation is subject to neither a prospectus approved by the financial market authority nor to control by any other body responsible for the protection of savings. The royalty right does not constitute a financial instrument within the meaning of Article L.211-1 of the Monetary and Financial Code. The rules on direct marketing of banking and financial products do not apply."
  },
  {
    question: "What happens if the financing objective is not reached on a property?",
    answer: "For each property, the sums paid by investors are deposited in an escrow bank account. The funds are only credited to the transaction if the fundraising campaign is successful. In the event that the transaction is not fully funded at the time of collection, we may be able to carry out the transaction on a credit in order to use the bank leverage effect or, if this is not possible, the funds will be returned in full to the investors, and you will be reimbursed 100%."
  },
  {
    question: "How will my personal data be used?",
    answer: 'In accordance with the French law "Informatique et Libertés", RoyalCity is committed to respecting the regulatory and ethical provisions on the protection of personal data. The information you provide when you register is stored on the platform in a secure and encrypted manner. Under no circumstances will RoyalCity sell your personal information to third parties.'
  },
  {
    question: "Are transactions on the platform secure?",

    answer: "RoyalCity uses the most advanced technical means to ensure the confidentiality and security of transactions on the platform. "
  }
]

export default legalQ;